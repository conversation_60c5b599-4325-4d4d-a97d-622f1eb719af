<?php

namespace App\Http\Controllers\Collaterals;

use App\Enums\Collateral\CollateralStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Collaterals\StoreCollateralRequest;
use App\Http\Requests\Collaterals\UpdateCollateralRequest;
use App\Http\Resources\Collaterals\CollateralDetailResource;
use App\Http\Resources\Collaterals\CollateralResource;
use App\Models\Collateral;
use App\Models\CollateralPropertyOwner;
use App\Models\CollateralValuer;
use App\Models\Company;
use App\Models\Contact;
use App\Models\Headquarter;
use App\Models\Team;
use App\Traits\QueryFilterableTrait;
use App\Traits\SelectionTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class CollateralController extends Controller
{
    use QueryFilterableTrait, SelectionTrait;

    /**
     * Display a listing of the collaterals.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Collateral::class);

        $query = Collateral::forUser()
            ->collateralOnly()
            ->withAuditUsers()
            ->with([
                'company.headquarter',
                'team',
                'typeSelection:id,value',
                'valuers',
            ]);

        if ($request->filled('name')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->input('name').'%')
                    ->orWhere('company_name', 'like', '%'.$request->input('name').'%');
            });
        }

        if ($request->filled('identity_no')) {
            $query->where(function ($q) use ($request) {
                $q->where('identity_no', 'like', '%'.$request->input('identity_no').'%')
                    ->orWhere('business_registration_no', 'like', '%'.$request->input('identity_no').'%');
            });
        }

        if (
            $request->filled('valuer') ||
            $request->filled('valuation_received_date_from') ||
            $request->filled('valuation_received_date_to') ||
            $request->filled('land_search_received_date_from') ||
            $request->filled('land_search_received_date_to')
        ) {
            $query->whereHas('valuers', function ($valuerQuery) use ($request) {
                if ($request->filled('valuer')) {
                    $valuerQuery->where('valuer', 'like', '%'.$request->input('valuer').'%');
                }
                if ($request->filled('valuation_received_date_from')) {
                    $valuerQuery->where('valuation_received_date', '>=', $request->input('valuation_received_date_from'));
                }
                if ($request->filled('valuation_received_date_to')) {
                    $valuerQuery->where('valuation_received_date', '<=', $request->input('valuation_received_date_to'));
                }
                if ($request->filled('land_search_received_date_from')) {
                    $valuerQuery->where('land_search_received_date', '>=', $request->input('land_search_received_date_from'));
                }
                if ($request->filled('land_search_received_date_to')) {
                    $valuerQuery->where('land_search_received_date', '<=', $request->input('land_search_received_date_to'));
                }
            });
        }

        $this->applySearchFilter($query, $request, 'type', 'selection_type_id');
        $this->applySorting($query, $request, 'created_at', 'desc');

        $collaterals = $this->applyPagination($query, $request, 10,
            fn ($collateral) => (new CollateralResource($collateral))->toArray($request));

        return Inertia::render('collaterals/Index', [
            'collaterals' => $collaterals,
            'filters' => $request->only(['name', 'identity_no', 'type', 'valuer', 'valuation_received_date_from', 'valuation_received_date_to', 'land_search_received_date_from', 'land_search_received_date_to', 'per_page', 'sort_field', 'sort_direction']),
            'statuses' => CollateralStatus::options(),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
        ]);
    }

    /**
     * Show the form for creating a new collateral.
     */
    public function create(): Response
    {
        $this->authorize('create', Collateral::class);

        return Inertia::render('collaterals/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'teams' => Team::getForDropdown(),
            'statuses' => CollateralStatus::options(),
            'customerTypes' => $this->getSelectionsOptionsForCategory('customer_type'),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
            'propertyTypes' => $this->getSelectionsOptionsForCategory('property_type'),
            'landCategories' => $this->getSelectionsOptionsForCategory('land_category'),
            'landStatuses' => $this->getSelectionsOptionsForCategory('land_status'),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'squareTypes' => $this->getSelectionsOptionsForCategory('square_unit'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'mobileCountries' => $this->getSelectionsOptionsForCategory('mobile_country'),
        ]);
    }

    /**
     * Store a newly created collateral in storage.
     */
    public function store(StoreCollateralRequest $request): RedirectResponse
    {
        $this->authorize('create', Collateral::class);

        try {
            DB::beginTransaction();

            $team = Team::find($request->team_id);

            // Create collateral
            $collateral = Collateral::create([
                'headquarter_id' => $team->headquarter_id,
                'company_id' => $team->company_id,
                ...$request->except(['property', 'valuers', 'property_owners']),
            ]);

            // Create property
            if ($request->has('property')) {
                $property = $collateral->property()->create([
                    ...$request->property,
                ]);

                // Create property address
                if ($request->has('property.address')) {
                    $property->address()->create([
                        ...($request->input('property.address')),
                        'is_primary' => true,
                    ]);
                }

                // Create property owners
                if ($request->has('property_owners')) {
                    foreach ($request->property_owners as $ownerData) {
                        $owner = $property->propertyOwners()->create([
                            'name' => $ownerData['name'],
                            'identity_no' => $ownerData['identity_no'],
                            'remark' => $ownerData['remark'] ?? null,
                        ]);

                        // Create owner address
                        if (isset($ownerData['address'])) {
                            $owner->address()->create([
                                ...$ownerData['address'],
                                'is_primary' => true,
                            ]);
                        }

                        // Create owner contacts
                        if (isset($ownerData['telephone'])) {
                            $owner->contacts()->create([
                                'category' => Contact::CATEGORY_TELEPHONE,
                                'selection_country_id' => $ownerData['selection_telephone_country_id'],
                                'contact' => $ownerData['telephone'],
                            ]);
                        }

                        if (isset($ownerData['mobile_phone'])) {
                            $owner->contacts()->create([
                                'category' => Contact::CATEGORY_MOBILE,
                                'selection_country_id' => $ownerData['selection_mobile_country_id'],
                                'contact' => $ownerData['mobile_phone'],
                                'can_receive_sms' => true,
                            ]);
                        }
                    }
                }
            }

            // Create valuers
            if ($request->has('valuers')) {
                foreach ($request->valuers as $valuerData) {
                    $collateral->valuers()->create([
                        ...$valuerData,
                    ]);
                }

            }

            DB::commit();

            return Redirect::route('collaterals.index')->with('success', 'Collateral created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create collateral: '.$e->getMessage());

            return Redirect::back()->with('error', 'Failed to create collateral: '.$e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified collateral.
     */
    public function show(Collateral $collateral): Response
    {
        $this->authorize('view', $collateral);

        $collateral->withAuditUsers();
        $collateral->load([
            'company.headquarter',
            'team',
            'typeSelection',
            'customerTypeSelection',
            'property.propertyTypesSelection',
            'property.landSizeSelection',
            'property.builtUpAreaSelection',
            'property.landCategorySelection',
            'property.landStatusSelection',
            'property.address',
            'property.propertyOwners.address',
            'property.propertyOwners.contacts',
            'valuers',
        ]);

        return Inertia::render('collaterals/Show', [
            'collateral' => (new CollateralDetailResource($collateral))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified collateral.
     */
    public function edit(Collateral $collateral): Response
    {
        $this->authorize('update', $collateral);

        $collateral->load([
            'company.headquarter',
            'team',
            'typeSelection',
            'property.propertyTypesSelection',
            'property.landSizeSelection',
            'property.builtUpAreaSelection',
            'property.landCategorySelection',
            'property.landStatusSelection',
            'property.address',
            'property.propertyOwners.address',
            'property.propertyOwners.contacts',
            'valuers',
        ]);

        return Inertia::render('collaterals/Edit', [
            'collateral' => (new CollateralDetailResource($collateral))->toArray(request()),
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'teams' => Team::getForDropdown(),
            'statuses' => CollateralStatus::options(),
            'customerTypes' => $this->getSelectionsOptionsForCategory('customer_type'),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
            'propertyTypes' => $this->getSelectionsOptionsForCategory('property_type'),
            'landCategories' => $this->getSelectionsOptionsForCategory('land_category'),
            'landStatuses' => $this->getSelectionsOptionsForCategory('land_status'),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'squareTypes' => $this->getSelectionsOptionsForCategory('square_unit'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'mobileCountries' => $this->getSelectionsOptionsForCategory('mobile_country'),
        ]);
    }

    /**
     * Update the specified collateral in storage.
     */
    public function update(UpdateCollateralRequest $request, Collateral $collateral): RedirectResponse
    {
        $this->authorize('update', $collateral);

        try {
            DB::beginTransaction();
            // Update collateral
            $collateral->update([
                ...$request->except(['property', 'address', 'valuers', 'property_owners']),
            ]);

            // Update or create property
            if ($request->has('property')) {
                $property = $collateral->property;

                if ($property) {
                    $property->update([
                        ...$request->property,
                    ]);
                } else {
                    $property = $collateral->property()->create([
                        ...$request->property,
                    ]);
                }

                // Update or create property address
                if ($request->has('address')) {
                    $address = $property->address;

                    if ($address) {
                        $address->update([
                            ...$request->address,
                        ]);
                    } else {
                        $property->address()->create([
                            ...$request->address,
                            'is_primary' => true,
                        ]);
                    }
                }

                // Update property owners
                if ($request->has('property_owners')) {
                    foreach ($request->property_owners as $ownerData) {
                        // Check if owner should be deleted
                        if (isset($ownerData['id']) && isset($ownerData['_delete']) && $ownerData['_delete']) {
                            $owner = CollateralPropertyOwner::find($ownerData['id']);
                            if ($owner) {
                                $owner->delete();
                            }

                            continue;
                        }

                        // Update or create owner
                        if (isset($ownerData['id'])) {
                            $owner = CollateralPropertyOwner::find($ownerData['id']);
                            if ($owner) {
                                $owner->update([
                                    'name' => $ownerData['name'],
                                    'identity_no' => $ownerData['identity_no'],
                                    'remark' => $ownerData['remark'] ?? null,
                                ]);
                            }
                        } else {
                            $owner = $property->propertyOwners()->create([
                                'name' => $ownerData['name'],
                                'identity_no' => $ownerData['identity_no'],
                                'remark' => $ownerData['remark'] ?? null,
                            ]);
                        }

                        // Update or create owner address
                        if (isset($ownerData['address'])) {
                            $address = $owner->address;

                            if ($address) {
                                $address->update([
                                    ...$ownerData['address'],
                                ]);
                            } else {
                                $owner->address()->create([
                                    ...$ownerData['address'],
                                    'is_primary' => true,
                                ]);
                            }
                        }

                        // Update or create owner contacts
                        if (isset($ownerData['telephone'])) {
                            $telephoneContact = $owner->contacts()
                                ->where('category', Contact::CATEGORY_TELEPHONE)
                                ->first();

                            if ($telephoneContact) {
                                $telephoneContact->update([
                                    'contact' => $ownerData['telephone'],
                                    'selection_country_id' => $ownerData['selection_telephone_country_id'],
                                ]);
                            } else {
                                $owner->contacts()->create([
                                    'category' => Contact::CATEGORY_TELEPHONE,
                                    'contact' => $ownerData['telephone'],
                                    'selection_country_id' => $ownerData['selection_telephone_country_id'],
                                ]);
                            }
                        }

                        if (isset($ownerData['mobile_phone'])) {
                            $mobileContact = $owner->contacts()
                                ->where('category', Contact::CATEGORY_MOBILE)
                                ->first();

                            if ($mobileContact) {
                                $mobileContact->update([
                                    'contact' => $ownerData['mobile_phone'],
                                    'selection_country_id' => $ownerData['selection_mobile_country_id'],
                                ]);
                            } else {
                                $owner->contacts()->create([
                                    'category' => Contact::CATEGORY_MOBILE,
                                    'contact' => $ownerData['mobile_phone'],
                                    'can_receive_sms' => true,
                                    'selection_country_id' => $ownerData['selection_mobile_country_id'],
                                ]);
                            }
                        }
                    }
                }
            }

            // Update valuers
            if ($request->has('valuers')) {
                foreach ($request->valuers as $valuerData) {
                    // Check if valuer should be deleted
                    if (isset($valuerData['id']) && isset($valuerData['_delete']) && $valuerData['_delete']) {
                        $valuer = CollateralValuer::find($valuerData['id']);
                        if ($valuer) {
                            $valuer->delete();
                        }

                        continue;
                    }

                    // Update or create valuer
                    if (isset($valuerData['id'])) {
                        $valuer = CollateralValuer::find($valuerData['id']);
                        if ($valuer) {
                            $valuer->update([
                                'valuation_amount' => $valuerData['valuation_amount'],
                                'valuer' => $valuerData['valuer'],
                                'valuation_received_date' => $valuerData['valuation_received_date'] ?? null,
                                'land_search_received_date' => $valuerData['land_search_received_date'] ?? null,
                            ]);
                        }
                    } else {
                        $collateral->valuers()->create([
                            'valuation_amount' => $valuerData['valuation_amount'],
                            'valuer' => $valuerData['valuer'],
                            'valuation_received_date' => $valuerData['valuation_received_date'] ?? null,
                            'land_search_received_date' => $valuerData['land_search_received_date'] ?? null,
                        ]);
                    }
                }
            }

            DB::commit();

            return Redirect::route('collaterals.index')
                ->with('success', 'Collateral updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update collateral: '.$e->getMessage());

            return Redirect::back()->withInput()->with('error', 'Failed to update collateral: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified collateral from storage.
     */
    public function destroy(Collateral $collateral): RedirectResponse
    {
        $this->authorize('delete', $collateral);

        try {
            DB::beginTransaction();

            // Delete related records
            if ($collateral->property) {
                // Delete property owners and their addresses and contacts
                foreach ($collateral->property->propertyOwners as $owner) {
                    if ($owner->address) {
                        $owner->address->delete();
                    }

                    foreach ($owner->contacts as $contact) {
                        $contact->delete();
                    }

                    $owner->delete();
                }

                // Delete property address
                if ($collateral->property->address) {
                    $collateral->property->address->delete();
                }

                $collateral->property->delete();
            }

            // Delete valuers
            foreach ($collateral->valuers as $valuer) {
                $valuer->delete();
            }

            // Delete collateral
            $collateral->delete();

            DB::commit();

            return Redirect::route('collaterals.index')->with('success', 'Collateral deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete collateral: '.$e->getMessage());

            return Redirect::back()->with('error', 'Failed to delete collateral: '.$e->getMessage());
        }
    }
}
