<?php

namespace App\Models;

use App\Enums\Loan\LoanInstallmentStatus;
use App\Enums\Loan\LoanTxnStatus;
use App\Traits\DateTimeConversion;
use App\Traits\GeneratesLoanTransactions;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanInstallment extends BaseModel
{
    use DateTimeConversion, GeneratesLoanTransactions, HasFactory;

    protected $fillable = [
        'uuid',
        'code',
        'loan_id',
        'pay_date',
        'tenure',
        'total_amount',
        'principle_amount',
        'interest_amount_net',
        'interest_amount_gross',
        'outstanding_balance_amount',
        'due_date',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'pay_date' => 'datetime',
        'due_date' => 'datetime',
        'total_amount' => 'decimal:2',
        'principle_amount' => 'decimal:2',
        'interest_amount_net' => 'decimal:2',
        'interest_amount_gross' => 'decimal:2',
        'outstanding_balance_amount' => 'decimal:2',
        'status' => LoanInstallmentStatus::class,
    ];

    public function getPayDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function getDueDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function scopeWithLoanId($query, $loanId)
    {
        return $query->where('loan_id', $loanId);
    }

    public function scopeWithCurrentInstallment($query)
    {
        $currentDate = Carbon::today()->toDateString();

        return $query->whereDate('pay_date', '<=', $currentDate)
            ->whereDate('due_date', '>=', $currentDate);
    }

    /**
     * Scope to get installments by status
     */
    public function scopeByStatus($query, array $status)
    {
        return $query->whereIn('status', $status);
    }

    public static function getBalanceInstallment(int $loanId, ?int $currentTenure = null, ?float $rebateAmount = 0): array
    {
        $totalArrearsAmount = 0;

        if ($currentTenure !== null) {
            $installmentArrear = self::withLoanId($loanId)
                ->byStatus([LoanInstallmentStatus::OVERDUE])
                ->where('tenure', '<', $currentTenure)
                ->selectRaw('SUM(principle_amount) as installment_arrears_amount')
                ->first();

            $totalArrearsAmount = (float) ($installmentArrear->installment_arrears_amount ?? 0);
        }

        $aggregates = self::withLoanId($loanId)
            ->selectRaw('
                COALESCE(SUM(principle_amount), 0) as total_principle,
                COALESCE(SUM(interest_amount_gross), 0) as total_interest
            ')
            ->first();

        $totalPrinciple = (float) $aggregates->total_principle;
        $totalInterest = (float) $aggregates->total_interest;
        $totalLoanAmount = $totalPrinciple + $totalInterest;

        $lateInterestCharges = LoanTxn::withLoanId($loanId)
            ->lateInterest()
            ->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])
            ->leftJoin('loan_payment_details', 'loan_txns.id', '=', 'loan_payment_details.loan_txn_id')
            ->selectRaw('
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS late_interest_charges_amount
            ')
            ->value('late_interest_charges_amount');

        $miscCharges = LoanTxn::withLoanId($loanId)
            ->miscCharge()
            ->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])
            ->leftJoin('loan_payment_details', 'loan_txns.id', '=', 'loan_payment_details.loan_txn_id')
            ->selectRaw('
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS misc_charges_amount
            ')
            ->value('misc_charges_amount');

        $legalCharges = LoanTxn::withLoanId($loanId)
            ->legalFee()
            ->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])
            ->leftJoin('loan_payment_details', 'loan_txns.id', '=', 'loan_payment_details.loan_txn_id')
            ->selectRaw('
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS legal_charges_amount
            ')
            ->value('legal_charges_amount');

        $postageCharges = LoanTxn::withLoanId($loanId)
            ->postage()
            ->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])
            ->leftJoin('loan_payment_details', 'loan_txns.id', '=', 'loan_payment_details.loan_txn_id')
            ->selectRaw('
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS postage_charges_amount
            ')
            ->value('postage_charges_amount');

        $totalPayment = LoanTxn::withLoanId($loanId)
            ->byStatus([LoanTxnStatus::PARTIALLY_PAID, LoanTxnStatus::PAID])
            ->installment()
            ->leftJoin('loan_payment_details', 'loan_txns.id', '=', 'loan_payment_details.loan_txn_id')
            ->selectRaw('SUM(loan_payment_details.amount) as total_payment_amount')
            ->value('total_payment_amount');

        $charges = [
            'late_interest_charges_amount' => (float) ($lateInterestCharges ?? 0),
            'postage_charges_amount' => (float) ($postageCharges ?? 0),
            'legal_charges_amount' => (float) ($legalCharges ?? 0),
            'misc_charges_amount' => (float) ($miscCharges ?? 0),
            'rebate_amount' => $rebateAmount,
            'total_payment_amount' => (float) ($totalPayment ?? 0),
        ];

        $totalBalanceAmount = $totalLoanAmount - $charges['total_payment_amount'];
        $totalOutstandingAmount = $totalBalanceAmount +
            $charges['late_interest_charges_amount'] +
            $charges['postage_charges_amount'] +
            $charges['legal_charges_amount'] +
            $charges['misc_charges_amount'] -
            $charges['rebate_amount'];

        $totals = array_merge([
            'total_loan_amount' => $totalLoanAmount,
            'total_balance_amount' => $totalBalanceAmount,
            'total_loan_principle_amount' => $totalPrinciple,
            'total_interest_charges_amount' => $totalInterest,
            'total_outstanding_amount' => $totalOutstandingAmount,
            'total_arrears_amount' => $totalArrearsAmount,
        ], $charges);

        return array_map(fn ($value) => number_format((float) $value, 2), $totals);
    }

    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the loan transactions for this installment.
     */
    public function loanTxns()
    {
        return $this->hasMany(LoanTxn::class);
    }
}
