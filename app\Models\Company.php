<?php

namespace App\Models;

use App\Enums\AccessControl\RoleName;
use App\Enums\Company\CompanyStatus;
use App\Traits\HandlesFileStorage;
use App\Traits\HasStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Company model for managing company information
 */
class Company extends BaseModel
{
    use HandlesFileStorage, HasFactory, HasStatus, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'name',
        'display_name',
        'business_registration_no',
        'old_business_registration_no',
        'logo',
        'is_headquarter',
        'status',
        'headquarter_id',
        'remark',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => CompanyStatus::class,
    ];

    /**
     * Set the display name and automatically set the name to uppercase.
     *
     * @param  string  $value
     * @return void
     */
    public function setDisplayNameAttribute($value)
    {
        $this->attributes['display_name'] = $value;
        $this->attributes['name'] = strtoupper($value);
    }

    /**
     * Scope a query to only include branch companies.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithNonHeadquarter($query)
    {
        return $query->where('companies.is_headquarter', false);
    }

    /**
     * Scope a query to get companies for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'display_name', 'headquarter_id')
            ->orderBy('display_name');
    }

    /**
     * Get the admin profiles associated with this company.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AdminProfile class, allowing this company to be associated
     * with various admin profiles through the 'companyable' polymorphic relation.
     */
    public function adminProfiles(): MorphToMany
    {
        return $this->morphedByMany(AdminProfile::class, 'companyable');
    }

    /**
     * Get the agent profiles associated with this company.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AgentProfile class, allowing this company to be associated
     * with various agent profiles through the 'companyable' polymorphic relation.
     */
    public function agentProfiles(): MorphToMany
    {
        return $this->morphedByMany(AgentProfile::class, 'companyable');
    }

    /**
     * Get the agent profiles associated with this company.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AgentOutcomeType class, allowing this company to be associated
     * with various agent profiles through the 'companyable' polymorphic relation.
     */
    public function agentOutcomeTypes(): MorphToMany
    {
        return $this->morphedByMany(AgentOutcomeType::class, 'companyable');
    }

    /**
     * Get the agent profiles associated with this company.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the AgentOutcome class, allowing this company to be associated
     * with various agent profiles through the 'companyable' polymorphic relation.
     */
    public function agentOutcomes(): MorphToMany
    {
        return $this->morphedByMany(AgentOutcome::class, 'companyable');
    }

    /**
     * Get the teams associated with this headquarter.
     *
     * This method defines a polymorphic many-to-many relationship
     * with the Team class, allowing this headquarter to be associated
     * with various teams through the 'teamable' polymorphic relation.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    /**
     * Get the collaterals associated with this company.
     */
    public function collaterals(): HasMany
    {
        return $this->hasMany(Collateral::class);
    }

    /**
     * Get the collaterals associated with this company.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(CustomerProfile::class);
    }

    /**
     * Get the parent company of this company.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'headquarter_id');
    }

    /**
     * Get the child companies of this company.
     */
    public function children(): HasMany
    {
        return $this->hasMany(Company::class, 'headquarter_id');
    }

    /**
     * Get the parent company of this company.
     */
    public function headquarter(): BelongsTo
    {
        return $this->belongsTo(Headquarter::class, 'headquarter_id');
    }

    /**
     * Get the logo URL for this company.
     */
    public function getLogoUrl(): ?array
    {
        return $this->logo ? app()->make(self::class)->getUploadedFile($this->logo) : null;
    }

    /**
     * Get companies for dropdown selection
     *
     * @param  array  $columns  Additional columns to select
     * @param  bool  $filterByUser  Whether to filter by user permissions
     * @return array Formatted companies data
     */
    public static function getForDropdown(bool $validateHeadquarterAccess = true, array $columns = [], bool $filterByUser = true): array
    {
        $defaultColumns = ['id', 'display_name', 'code', 'headquarter_id', 'is_headquarter'];
        $selectColumns = $columns ? array_merge($defaultColumns, $columns) : $defaultColumns;

        $query = self::forDropdown()->select($selectColumns);

        if ($filterByUser && ! auth()->user()->hasRole(RoleName::SUPER_ADMIN)) {
            $user = auth()->user();

            $userCompanyIds = $user->adminProfiles
                ->flatMap(function ($profile) use ($validateHeadquarterAccess) {
                    $companyIds = collect();

                    if ($profile->headquarters()->exists()) {
                        if ($validateHeadquarterAccess) {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->where('is_headquarter', true)->pluck('id')
                            );
                        } else {
                            $companyIds = $companyIds->merge(
                                Company::whereIn('headquarter_id', $profile->headquarters()->pluck('id'))->pluck('id')
                            );
                        }
                    }

                    $companyIds = $companyIds->merge($profile->companies()->pluck('id'));

                    $teamCompanyIds = $profile->teams()
                        ->get()
                        ->flatMap(function ($team) {
                            $companyIds = $team->companies()->pluck('id');

                            $headquarterIds = $team->companies()
                                ->where('is_headquarter', true)
                                ->pluck('id');

                            if ($headquarterIds->isNotEmpty()) {
                                $companyIds = $companyIds->merge(
                                    Company::whereIn('headquarter_id', $headquarterIds)->pluck('id')
                                );
                            }

                            return $companyIds;
                        });

                    return $companyIds->merge($teamCompanyIds);
                })
                ->unique();

            $query->whereIn('id', $userCompanyIds);
        }

        return $query->get()
            ->map(fn ($company) => [
                'id' => $company->id,
                'display_name' => $company->display_name,
                'code' => $company->code,
                'headquarter_id' => $company->headquarter_id,
                'is_headquarter' => $company->is_headquarter,
            ])
            ->toArray();
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function withUserAccess($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return self::query();
        }

        return self::whereIn('headquarter_id', $user->getAccessibleHeadquarterIds());
    }

    /**
     * Check if the given user has access to this headquarter
     *
     * @param  \App\Models\User|null  $user
     */
    public function isAccessibleBy($user = null): bool
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return true;
        }

        $accessibleIds = self::forUser($user)->pluck('id')->toArray();

        return in_array($this->id, $accessibleIds);
    }

    /**
     * The "boot" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix();
    }
}
