<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Headquarter;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class HeadquarterAdminUserSeeder extends Seeder
{
    public function run(): void
    {
        $headquarter = Headquarter::firstWhere('name', 'HEADQUARTER001');
        if (! $headquarter) {
            $this->command->error('Headquarter not found. Please run HeadquarterSeeder first.');

            return;
        }

        $hqAdminRole = Role::firstWhere('name', 'Headquarter Admin');
        if (! $hqAdminRole) {
            $this->command->error('Headquarter Admin role not found. Please run RoleSeeder first.');

            return;
        }

        $hqAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'HQAdmin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $hqAdmin->hasRole($hqAdminRole)) {
            $hqAdmin->assignRole($hqAdminRole);
        }

        if (! $hqAdmin->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'headquarter_id' => $headquarter->id,
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($hqAdmin->id);

            $adminProfile->headquarters()->syncWithoutDetaching([$headquarter->id]);
        }

        $this->command->info('Headquarter admin user created successfully.');
    }
}
