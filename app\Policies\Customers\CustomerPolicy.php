<?php

namespace App\Policies\Customers;

use App\Enums\AccessControl\PermissionName;
use App\Models\CustomerProfile;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any customers.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can view the customer.
     *
     * @return bool
     */
    public function view(User $user, CustomerProfile $customer)
    {
        return $user->hasPermissionTo(PermissionName::READ_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can create customers.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can update the customer.
     *
     * @return bool
     */
    public function update(User $user, CustomerProfile $customer)
    {
        $customerCompany = $customer->company1()->first();

        if ($user->isHeadquarter() && $user->hasHeadquarterAccess($customerCompany->headquarter_id) && $customerCompany->is_headquarter) {
            return $user->hasPermissionTo(PermissionName::UPDATE_CUSTOMERS->value);
        }

        if (! $user->hasCompanyAccess($customer->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can delete the customer.
     *
     * @return bool
     */
    public function delete(User $user, CustomerProfile $customer)
    {
        if (! $user->hasCompanyAccess($customer->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can restore the customer.
     *
     * @return bool
     */
    public function restore(User $user, CustomerProfile $customer)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_CUSTOMERS->value);
    }

    /**
     * Determine whether the user can permanently delete the customer.
     *
     * @return bool
     */
    public function forceDelete(User $user, CustomerProfile $customer)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_CUSTOMERS->value);
    }
}
