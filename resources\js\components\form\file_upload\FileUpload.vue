<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import InputError from '@/components/InputError.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ref, watch } from 'vue';

interface ExistingFile {
    name: string;
    size: number;
    url: string;
}

interface Base64File {
    name: string;
    size: number;
    base64: string;
}

interface Props {
    id: string;
    label?: string;
    modelValue: File | ExistingFile | Base64File | null;
    description?: string;
    required?: boolean;
    error?: string;
    class?: string;
    labelClass?: string;
    inputClass?: string;
    fileFormat?: string;
    maxSizeMB?: number;
    isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    label: 'Logo',
    description: 'For receipt display purpose',
    required: false,
    class: 'flex cursor-pointer flex-col items-center justify-center rounded-md border p-2 text-center text-gray-500',
    fileFormat: '.jpeg,.jpg,.png',
    maxSizeMB: 5,
    isEdit: false,
});

const emit = defineEmits(['update:modelValue']);

const filePreviewUrl = ref<string | null>(null);
const isImage = ref(false);
const MAX_FILE_SIZE = props.maxSizeMB * 1024 * 1024;

function isFile(val: unknown): val is File {
    return typeof File !== 'undefined' && val instanceof File;
}

function isExistingFile(val: unknown): val is ExistingFile {
    return val != null && typeof val === 'object' && 'url' in val && 'name' in val && 'size' in val;
}

function isBase64File(val: unknown): val is Base64File {
    return val != null && typeof val === 'object' && 'base64' in val;
}

function toBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

async function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (!file) return;

    if (file.size > MAX_FILE_SIZE) {
        alert(`File exceeds max size of ${props.maxSizeMB}MB`);
        target.value = '';
        return;
    }

    if (props.isEdit) {
        const base64 = await toBase64(file);
        emit('update:modelValue', {
            name: file.name,
            size: file.size,
            base64,
        });
    } else {
        emit('update:modelValue', file); // binary File for create
    }

    target.value = '';
}

watch(
    () => props.modelValue,
    (val) => {
        filePreviewUrl.value = null;
        isImage.value = false;

        if (!val) return;

        let previewSrc = '';

        if (isFile(val)) {
            previewSrc = URL.createObjectURL(val);
        } else if (isExistingFile(val)) {
            previewSrc = val.url.replace(/\\/g, '');
        } else if (isBase64File(val)) {
            previewSrc = val.base64;
        }

        filePreviewUrl.value = previewSrc;
        isImage.value = true;
    },
    { immediate: true },
);

function removeFile() {
    if (filePreviewUrl.value && isFile(props.modelValue)) {
        URL.revokeObjectURL(filePreviewUrl.value);
    }
    filePreviewUrl.value = null;
    isImage.value = false;
    emit('update:modelValue', null);
}

function viewFile() {
    if (isFile(props.modelValue)) {
        const url = URL.createObjectURL(props.modelValue);
        window.open(url, '_blank');
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    } else if (isExistingFile(props.modelValue)) {
        window.open(props.modelValue.url, '_blank');
    } else if (isBase64File(props.modelValue)) {
        try {
            const base64Data = props.modelValue.base64.split(',')[1] || props.modelValue.base64;
            const mimeType = props.modelValue.base64.match(/data:([^;]+)/)?.[1] || 'application/octet-stream';
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);

            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: mimeType });
            const url = URL.createObjectURL(blob);

            window.open(url, '_blank');
            setTimeout(() => URL.revokeObjectURL(url), 1000);
        } catch (error) {
            console.error('Error creating blob from base64:', error);
            alert('Unable to view file. The file data may be corrupted.');
        }
    }
}

function formatFileSize(size: number): string {
    if (!size) return '';
    if (size < 1024) return `${size} B`;
    else if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    else return `${(size / (1024 * 1024)).toFixed(2)} MB`;
}
</script>

<template>
    <div>
        <Label class="mb-1">{{ props.label }}</Label>
        <p class="text-steel mb-2 text-sm italic">{{ props.description }}</p>

        <Label :for="props.id" :class="props.class">
            <template v-if="modelValue">
                <div
                    class="grid h-[98px] w-full grid-cols-[74px_1fr_40px_40px] items-center gap-2 rounded-md border border-dashed bg-gray-50 p-2 hover:bg-gray-50"
                >
                    <div class="flex h-[74px] w-[74px] items-center justify-center rounded border bg-white">
                        <img v-if="isImage && filePreviewUrl" :src="filePreviewUrl" alt="preview" class="h-[74px] w-[74px] rounded object-contain" />
                        <FaIcon v-else name="file" class="text-2xl text-gray-500" />
                    </div>

                    <div class="flex flex-col truncate text-left">
                        <span class="truncate font-medium text-black">
                            {{ modelValue.name }}
                        </span>
                        <span class="text-xs text-gray-500">
                            {{ formatFileSize(modelValue.size) }}
                        </span>
                    </div>

                    <button type="button" @click.stop.prevent="removeFile" class="text-red-500 hover:text-red-700">
                        <FaIcon name="trash" />
                    </button>
                    <button type="button" @click.stop.prevent="viewFile" class="text-blue-600 hover:text-blue-800">
                        <FaIcon name="eye" />
                    </button>
                </div>
            </template>

            <template v-else>
                <div class="h-[98px] w-full rounded-md border-2 border-dashed p-4 hover:bg-gray-50">
                    <span class="text-lavender text-[20px]">
                        <FaIcon name="cloud-arrow-up" />
                    </span>
                    <span class="block text-xs">Drag & Drop or Click to Browse</span>
                    <span class="block text-xs">Max: {{ props.maxSizeMB }}MB (JPEG, JPG, PNG)</span>
                </div>
            </template>

            <Input :id="props.id" type="file" :accept="props.fileFormat" :class="props.inputClass" class="hidden" @change="handleFileUpload" />
        </Label>

        <InputError class="mt-1" :message="props.error" />
    </div>
</template>
