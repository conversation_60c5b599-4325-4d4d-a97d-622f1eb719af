<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class HeadquarterLoanReviewerUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'HQ Team 1');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $hqLoanReviewerRole = Role::firstWhere('name', 'Headquarter Loan Reviewer');
        if (! $hqLoanReviewerRole) {
            $this->command->error('Headquarter Loan Reviewer role not found. Please run RoleSeeder first.');

            return;
        }

        $hqLoanReviewer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'HQLoanReviewer',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $hqLoanReviewer->hasRole($hqLoanReviewerRole)) {
            $hqLoanReviewer->assignRole($hqLoanReviewerRole);
        }

        if (! $hqLoanReviewer->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'headquarter_id' => $team->headquarter_id,
                'company_id' => $team->company_id,
                'team_id' => $team->id,
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($hqLoanReviewer->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Headquarter loan reviewer user created successfully.');
    }
}
