<?php

namespace App\Policies\Agents;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\AgentOutcome;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AgentOutcomePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any agent outcomes.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can view the agent outcome.
     *
     * @return bool
     */
    public function view(User $user, AgentOutcome $agentOutcome)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can create agent outcomes.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can update the agent outcome.
     *
     * @return bool
     */
    public function update(User $user, AgentOutcome $agentOutcome)
    {
        $agentOutcomeCompany = $agentOutcome->companies->first();

        if ($user->isHeadquarter() && $user->hasHeadquarterAccess($agentOutcomeCompany->headquarter_id) && $agentOutcomeCompany->is_headquarter) {
            return $user->hasPermissionTo(PermissionName::UPDATE_AGENT_OUTCOMES->value);
        }

        if (! $user->hasCompanyAccess($agentOutcome->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can delete the agent outcome.
     *
     * @return bool
     */
    public function delete(User $user, AgentOutcome $agentOutcome)
    {
        $agentOutcomeCompany = $agentOutcome->companies->first();

        if ($user->isHeadquarter() && $user->hasHeadquarterAccess($agentOutcomeCompany->headquarter_id) && $agentOutcomeCompany->is_headquarter) {
            return $user->hasPermissionTo(PermissionName::DELETE_AGENT_OUTCOMES->value);
        }

        if (! $user->hasCompanyAccess($agentOutcome->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can restore the agent outcome.
     *
     * @return bool
     */
    public function restore(User $user, AgentOutcome $agentOutcome)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_AGENT_OUTCOMES->value);
    }

    /**
     * Determine whether the user can permanently delete the agent outcome.
     *
     * @return bool
     */
    public function forceDelete(User $user, AgentOutcome $agentOutcome)
    {
        // Only Super Administrator can force delete agent outcomes
        return $user->hasRole(RoleName::SUPER_ADMIN->value);
    }
}
