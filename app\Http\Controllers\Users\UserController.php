<?php

namespace App\Http\Controllers\Users;

use App\Enums\User\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Users\StoreUserRequest;
use App\Http\Requests\Users\UpdateUserRequest;
use App\Http\Requests\Users\UpdateUserStatusRequest;
use App\Http\Resources\Users\UserResource;
use App\Models\AdminProfile;
use App\Models\Company;
use App\Models\Headquarter;
use App\Models\Role;
use App\Models\Team;
use App\Models\User;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', User::class);

        $currentUserLevel = auth()->user()->getHighestRoleLevel();

        $query = User::query()
            ->excludeCurrent()
            ->select('id', 'uuid', 'username', 'email', 'status', 'updated_at', 'updated_by')
            ->with(['roles:id,name,level,is_headquarter', 'updatedBy:id,username'])
            ->whereHas('roles', function ($query) use ($currentUserLevel) {
                $query->maxLevel($currentUserLevel);
            });

        if (auth()->user()->getAccessibleHeadquarterIds()->isNotEmpty() || auth()->user()->getAccessibleCompanyIds()->isNotEmpty() || auth()->user()->getAccessibleTeamIds()->isNotEmpty()) {
            $query->whereHas('adminProfiles', function ($profileQuery) {
                $headquarterIds = auth()->user()->getAccessibleHeadquarterIds();
                $companyIds = auth()->user()->getAccessibleCompanyIds();
                $teamIds = auth()->user()->getAccessibleTeamIds();

                $profileQuery->whereHas('headquarters', function ($hq) use ($headquarterIds) {
                    $hq->whereIn('id', $headquarterIds);
                })
                    ->orWhereHas('companies', function ($company) use ($companyIds) {
                        $company->whereIn('id', $companyIds);
                    })
                    ->orWhereHas('teams', function ($team) use ($teamIds) {
                        $team->whereIn('id', $teamIds);
                    });
            });
        }

        if ($request->filled('headquarter_name') || $request->filled('company_name') || $request->filled('team_name')) {
            $query->whereHas('adminProfiles', function ($profileQuery) use ($request) {

                if ($request->filled('headquarter_name')) {
                    $searchTerm = '%'.$request->input('headquarter_name').'%';
                    $profileQuery->whereHas('headquarter', function ($hq) use ($searchTerm) {
                        $hq->where('display_name', 'like', $searchTerm);
                    });
                }

                if ($request->filled('company_name')) {
                    $searchTerm = '%'.$request->input('company_name').'%';
                    $profileQuery->whereHas('company', function ($company) use ($searchTerm) {
                        $company->where('display_name', 'like', $searchTerm)
                            ->where('is_headquarter', false);
                    });
                }

                if ($request->filled('team_name')) {
                    $searchTerm = '%'.$request->input('team_name').'%';
                    $profileQuery->whereHas('team', function ($team) use ($searchTerm) {
                        $team->where('name', 'like', $searchTerm);
                    });
                }
            });
        }

        $this->applySearchFilter($query, $request, 'username');
        $this->applyRelationFilter($query, $request, 'role', 'roles', 'name');
        $this->applyStatusFilter($query, $request);

        $validSortFields = ['headquarter:display_name', 'company:display_name', 'team:name'];
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction');

        if (in_array($sortField, $validSortFields)) {
            switch ($sortField) {
                case 'headquarter:display_name':
                    $query->leftJoin('headquarters', 'admin_profiles.headquarter_id', '=', 'headquarters.id')
                        ->orderBy('headquarters.display_name', $sortDirection);
                    break;

                case 'company:display_name':
                    $query->leftJoin('companies', 'admin_profiles.company_id', '=', 'companies.id')
                        ->orderBy('companies.display_name', $sortDirection);
                    break;

                case 'team:name':
                    $query->leftJoin('teams', 'admin_profiles.team_id', '=', 'teams.id')
                        ->orderBy('teams.name', $sortDirection);
                    break;
            }
        } else {
            Log::info('Invalid sort field: '.$sortField);
            $this->applySorting($query, $request);
        }

        $users = $this->applyPagination($query, $request, 10,
            fn ($user) => (new UserResource($user))->toArray($request));

        return Inertia::render('users/Index', [
            'users' => $users,
            'filters' => $request->only(['username', 'headquarter_name', 'company_name', 'team_name', 'role', 'status', 'per_page', 'sort_field', 'sort_direction']),
            'availableRoles' => Role::getAvailableRoles($currentUserLevel),
            'statuses' => UserStatus::options(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', User::class);

        return Inertia::render('users/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(false),
            'teams' => Team::getForDropdown(),
            'roles' => auth()->user()->getForAssignableRoles(),
            'defaultStatus' => UserStatus::ACTIVE->value,
            'statuses' => UserStatus::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request): RedirectResponse
    {
        $this->authorize('create', User::class);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $user = User::create([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'status' => $validated['status'],
            ]);

            $user->syncRoles([$validated['role']]);

            $adminProfile = AdminProfile::create([
                'headquarter_id' => $validated['headquarter_id'],
                'company_id' => $validated['company_id'],
                'team_id' => $validated['team_id'],
                'uuid' => Str::uuid(),
            ]);

            $adminProfile->users()->attach($user->id);

            if ($validated['team_id']) {
                $adminProfile->teams()->attach($validated['team_id']);
            } elseif ($validated['company_id']) {
                $adminProfile->companies()->attach($validated['company_id']);
            } elseif ($validated['headquarter_id']) {
                $adminProfile->headquarters()->attach($validated['headquarter_id']);
            }

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create user: '.$e->getMessage());

            return back()->with('error', 'Failed to create user: '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): Response
    {
        $this->authorize('view', $user);

        $user->load(['roles', 'updatedBy', 'createdBy']);

        return Inertia::render('users/Show', [
            'user' => (new UserResource($user))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user): Response
    {
        $this->authorize('update', $user);

        $adminProfile = $user->adminProfiles->first();
        $headquarterId = $companyId = $teamId = null;

        if (! $adminProfile) {
            // No admin profile, all IDs remain null
        } elseif ($headquarter = $adminProfile->headquarters->first()) {
            $headquarterId = $headquarter->id;
        } elseif ($company = $adminProfile->companies->first()) {
            $companyId = $company->id;
            $headquarterId = $company->headquarter_id && $company->headquarter ? $company->headquarter->id : null;
        } elseif ($team = $adminProfile->teams->first()) {
            $teamId = $team->id;

            if ($teamCompany = $team->companies->first()) {
                $companyId = $teamCompany->id;
                $headquarterId = $teamCompany->headquarter_id && $teamCompany->headquarter ? $teamCompany->headquarter->id : null;
            }
        }

        return Inertia::render('users/Edit', [
            'user' => [
                'id' => $user->id,
                'uuid' => $user->uuid,
                'username' => $user->username,
                'email' => $user->email,
                'status' => $user->status->value,
                'roles' => $user->roles->pluck('id'),
            ],
            'adminProfile' => $adminProfile ? [
                'id' => $adminProfile->id,
                'code' => $adminProfile->code,
                'name' => $adminProfile->name,
                'headquarter_id' => $headquarterId,
                'company_id' => $companyId,
                'team_id' => $teamId,
            ] : null,
            'roles' => auth()->user()->getForAssignableRoles(),
            'statuses' => UserStatus::options(),
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(false),
            'teams' => Team::getForDropdown(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user): RedirectResponse
    {
        $this->authorize('update', $user);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $user->update([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'status' => $validated['status'],
            ]);

            if ($request->filled('password')) {
                $user->update(['password' => Hash::make($validated['password'])]);
            }

            $user->syncRoles([$validated['role']]);

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user: '.$e->getMessage());

            return back()->with('error', 'Failed to update user: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        $this->authorize('delete', $user);

        try {
            DB::beginTransaction();

            $adminProfile = $user->adminProfiles()->exists();

            if ($adminProfile) {
                $user->adminProfiles()->detach();
                $user->adminProfiles()->delete();
            }

            $user->delete();

            DB::commit();

            return redirect()->route('users.index')->with('success', 'User deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete user: '.$e->getMessage());

            return redirect()->back()->with('error', 'Failed to delete user. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateUserStatusRequest $request, User $user): RedirectResponse
    {
        $this->authorize('update', $user);

        try {
            $user->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'User status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update user status. '.$e->getMessage());
        }
    }
}
