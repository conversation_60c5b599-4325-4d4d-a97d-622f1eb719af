<?php

namespace App\Http\Resources\Loans;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request, bool $detailed = false): array
    {
        $customer = $this->whenLoaded('loanCustomerProfiles', fn () => $this->loanCustomerProfiles->sortByDesc('is_primary')->map(fn ($profile) => [
            'name' => $profile->name,
            'identity_no' => $profile->identity_no,
            'is_primary' => $profile->is_primary,
        ]));

        $baseData = [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'customer_id' => $this->customer_id,
            'company_id' => $this->company_id,
            'customer' => $customer,
            'company' => $this->company,
            'team_id' => $this->team_id,
            'team' => $this->team,
            'agent_id' => $this->agent_id,
            'agent' => $this->agent,
            'selection_type_id' => $this->selection_type_id,
            'type' => $this->type,
            'commencement_date' => $this->loanDetail->commencement_date,
            'loan_principle_amount' => number_format($this->loanDetail->loan_principle_amount, 2),
            'status' => $this->status->value,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
            'canUpdate' => $request->user()->can('update', $this->resource),
            'canView' => $request->user()->can('view', $this->resource),
        ];

        return $baseData;
    }
}
