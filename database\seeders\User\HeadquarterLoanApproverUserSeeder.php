<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class HeadquarterLoanApproverUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'HQ Team 1');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $hqLoanApproverRole = Role::firstWhere('name', 'Headquarter Loan Approver');
        if (! $hqLoanApproverRole) {
            $this->command->error('Headquarter Loan Approver role not found. Please run RoleSeeder first.');

            return;
        }

        $hqLoanApprover = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'HQLoanApprover',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $hqLoanApprover->hasRole($hqLoanApproverRole)) {
            $hqLoanApprover->assignRole($hqLoanApproverRole);
        }

        if (! $hqLoanApprover->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'headquarter_id' => $team->headquarter_id,
                'company_id' => $team->company_id,
                'team_id' => $team->id,
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($hqLoanApprover->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Headquarter loan approver user created successfully.');
    }
}
