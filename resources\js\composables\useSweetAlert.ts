import Swal from 'sweetalert2';

export function useSweetAlert() {
    const confirm = async (options: {
        title: string;
        html?: string;
        text?: string;
        confirmText?: string;
        cancelText?: string;
        showTextarea?: boolean;
        textareaLabel?: string;
        textareaRequired?: boolean;
    }) => {
        let htmlContent = options.html || options.text || '';
        if (options.showTextarea) {
            const label = options.textareaLabel || 'Reason';
            const required = options.textareaRequired ? 'required' : '';
            htmlContent += `
                <div style="text-align: left; margin-top: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">${label} ${options.textareaRequired ? '<span style="color: red;">*</span>' : ''}</label>
                    <textarea
                        id="swal-textarea"
                        placeholder="Enter ${label.toLowerCase()}..."
                        style="width: 100%; min-height: 80px; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; resize: vertical;"
                        ${required}
                    ></textarea>
                </div>
            `;
        }

        const result = await Swal.fire({
            title: options.title,
            html: htmlContent,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: options.confirmText || 'Yes',
            cancelButtonText: options.cancelText || 'Cancel',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded',
                cancelButton: 'bg-red-600 hover:bg-red-400 text-white px-4 py-2 rounded ml-2',
            },
            preConfirm: () => {
                if (options.showTextarea) {
                    const textarea = document.getElementById('swal-textarea') as HTMLTextAreaElement;
                    const value = textarea?.value?.trim();

                    if (options.textareaRequired && !value) {
                        Swal.showValidationMessage('This field is required');
                        return false;
                    }

                    return { confirmed: true, textareaValue: value };
                }
                return { confirmed: true };
            },
        });

        return {
            isConfirmed: result.isConfirmed,
            textareaValue: result.value?.textareaValue || '',
        };
    };

    const success = (text: string, method?: string) => {
        const { past } = getActionText(method);
        Swal.fire({
            icon: 'success',
            title: 'Successfully ' + past,
            text,
            confirmButtonText: 'OK',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded',
            },
        });
    };

    const error = (text: string, method?: string) => {
        const { present } = getActionText(method);
        Swal.fire({
            icon: 'error',
            title: `${present} Failed`,
            text,
            confirmButtonText: 'OK',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded',
            },
        });
    };

    const getActionText = (method?: string) => {
        if (!method) return { present: 'Update', past: 'Updated' };

        switch (method) {
            case 'post':
                return { present: 'Creation', past: 'Created' };
            case 'put':
            case 'patch':
                return { present: 'Update', past: 'Updated' };
            case 'delete':
                return { present: 'Deletion', past: 'Deleted' };
            default:
                return { present: 'Update', past: 'Updated' };
        }
    };

    return { confirm, success, error };
}
