import type { PageProps } from '@inertiajs/core';
import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
}

export interface SharedData extends PageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
}

export interface User {
    id: number;
    uuid: string;
    username: string;
    email: string | null;
    status: number;
    roles: number[];
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface CreatedBy {
    id: number;
    name: string;
}

export interface UpdatedBy {
    id: number;
    name: string;
}

export interface AdminProfile {
    id: number;
    code: string;
    headquarter_id: number | null;
    company_id: number | null;
    team_id: number | null;
}

export interface Role {
    id: number;
    name: string;
    level: number;
    can_see_same_level: boolean;
    permissions: string[];
    is_required_headquarter: boolean;
    is_required_company: boolean;
    is_required_team: boolean;
    is_headquarter: boolean;
    created_at: string;
    updated_at: string;
}

export interface Permission {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

export interface Headquarter {
    id: number;
    uuid: string;
    code: string;
    name: string;
    display_name: string;
    business_registration_no: string;
    old_business_registration_no: string;
    status: number;
    headquarter_company?: Company | null;
    updated_at?: string | null;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface Company {
    id: number;
    uuid: string;
    code: string;
    name: string;
    display_name: string;
    business_registration_no: string;
    old_business_registration_no: string;
    status: number;
    headquarter_id: number | null;
    is_headquarter: boolean;
    headquarter?: Headquarter | null;
    updated_at?: string | null;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface Team {
    id: number;
    uuid: string;
    company_id: number | null;
    company_name: string | null;
    code: string;
    name: string;
    email: string | null;
    website: string | null;
    status: number;
    company: Company | null;
    headquarter: Headquarter | null;
    address: Address | null;
    contact: Contact | null;
    updated_at: string;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface Agent {
    id: number;
    uuid: string;
    code: string;
    name: string;
    display_name: string;
    email: string;
    remark: string;
    status: number;
    headquarter: Headquarter | string | null;
    company_id: number | null;
    company: Company | string | null;
    companies: Company[];
    updated_at: string;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface OutcomeType {
    id: number;
    uuid: string;
    name: string;
    headquarter: Headquarter | string | null;
    company: Company | string | null;
    companies: Company[];
    updated_at: string;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface Outcome {
    id: number;
    uuid: string;
    headquarter: Headquarter | null;
    company: Company | null;
    agent: Agent | null;
    outcomeType: string;
    amount: number;
    remark: string | null;
    updated_at: string;
    updated_by: UpdatedBy | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

export interface Address {
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    selection_state_id: number | null;
    selection_country_id: number | null;
}

export interface Contact {
    selection_country_id: number | null;
    contact: string | null;
}

export interface Selection {
    id: number;
    uuid: string;
    category: string;
    value: string;
    description: string | null;
    sort_order: number;
    status: number;
    headquarter_id: number | null;
    company_id: number | null;
    team_id: number | null;
    is_headquarter: boolean;
    updated_at: string;
    updated_by: UpdatedBy | null;
}

export interface LoanInstallment {
    id: number;
    uuid: string;
    code: string;
    loan_id: number;
    loan?: {
        id: number;
        uuid: string;
        code: string;
        status: number;
    };
    pay_date: string;
    due_date: string;
    tenure: number;
    total_amount: string;
    principle_amount: string;
    interest_amount_net: string;
    interest_amount_gross: string;
    late_charge_amount: string;
    postage_amount: string;
    outstanding_balance_amount: string;
    status: number;
    status_label: string;
    is_overdue: boolean;
    days_overdue: number;
    created_at: string;
    updated_at: string;
    created_by?: CreatedBy | null;
    updated_by?: UpdatedBy | null;
}

export interface LoanTransaction {
    id: number;
    type: string;
    amount: string;
    tenure: number;
    date: string;
    model: any;
}

export interface LoanInstallmentCurrent {
    tenure: number;
    due_date: string;
}

export interface LoanInstallmentBalance {
    total_loan_amount: string;
    total_balance_amount: string;
    total_loan_principle_amount: string;
    total_interest_charges_amount: string;
    late_interest_charges_amount: string;
    postage_charges_amount: string;
    legal_charges_amount: string;
    misc_charges_amount: string;
    rebate_amount: string;
    total_payment_amount: string;
    total_outstanding_amount: string;
    total_arrears_amount: string;
}

export interface LoanTransactionBalance {
    installment_owe_amount: string;
    late_interest_charges_amount: string;
    postage_charges_amount: string;
    legal_charges_amount: string;
    misc_charges_amount: string;
    total_balance_amount: string;
}

export interface LoanPaymentRecord {
    code: string;
    txn_date: string;
    txn_type: string;
    payment_ref_code: string;
    payment_method: string;
    payment_date: string;
    amount: string;
    rebate_amount: string;
    remark: string | null;
    status: number;
    created_at: string;
    updated_at: string;
    created_by?: CreatedBy | null;
    updated_by?: UpdatedBy | null;
}

export type BreadcrumbItemType = BreadcrumbItem;
