<?php

namespace App\Http\Resources\AccessControl;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'level' => $this->level,
            'can_see_same_level' => $this->can_see_same_level,
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->pluck('name');
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'canUpdate' => $request->user()->can('update', $this->resource),
            'canView' => $request->user()->can('view', $this->resource),
        ];
    }
}
