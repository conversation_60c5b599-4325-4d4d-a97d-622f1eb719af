<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class TeamAdminUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'Company Team 2');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $teamAdminRole = Role::firstWhere('name', 'Team Admin');
        if (! $teamAdminRole) {
            $this->command->error('Team Admin role not found. Please run RoleSeeder first.');

            return;
        }

        $teamAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'TeamAdmin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $teamAdmin->hasRole($teamAdminRole)) {
            $teamAdmin->assignRole($teamAdminRole);
        }

        if (! $teamAdmin->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'headquarter_id' => $team->headquarter_id,
                'company_id' => $team->company_id,
                'team_id' => $team->id,
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($teamAdmin->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Team admin user created successfully.');
    }
}
