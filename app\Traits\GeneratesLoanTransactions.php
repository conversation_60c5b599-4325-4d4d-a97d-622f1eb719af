<?php

namespace App\Traits;

use App\Enums\Loan\LoanPaymentStatus;
use App\Enums\Loan\LoanStatus;
use App\Enums\Loan\LoanTxnStatus;
use App\Enums\Loan\LoanTxnType;
use App\Models\Loan;
use App\Models\LoanPayment;
use App\Models\LoanPaymentDetail;
use App\Models\LoanTxn;
use App\Models\LoanTxnDetail;
use App\Models\LoanTxnType as LoanTxnTypeModel;
use App\Models\Selection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Trait GeneratesLoanTransactions
 *
 * This trait provides methods to generate loan transactions and transaction details.
 * It can be used by models that need to create loan transactions, reducing code duplication.
 */
trait GeneratesLoanTransactions
{
    /**
     * Generate loan transactions for installments
     * This method creates loan transactions and transaction details for each installment
     *
     * @param  array  $installments  Array of installment data
     * @param  int  $loanId  The loan ID
     * @return array Array of created transaction IDs
     *
     * @throws \RuntimeException
     */
    public function generateInstallmentTransactions(array $installments, int $loanId): array
    {
        try {
            $transactionIds = [];

            DB::transaction(function () use ($installments, $loanId, &$transactionIds) {
                foreach ($installments as $installment) {
                    $transaction = $this->createLoanTransaction([
                        'loan_id' => $loanId,
                        'txn_date' => $installment['pay_date'] ?? now(),
                        'loan_installment_id' => $installment['id'] ?? null,
                        'loan_txn_type_id' => LoanTxnType::INSTALLMENT->value,
                        'tenure' => $installment['tenure'] ?? null,
                        'amount' => $installment['total_amount'],
                        'status' => LoanTxnStatus::UNPAID,
                        'remark' => 'Instalment for tenure '.($installment['tenure'] ?? ''),
                    ]);

                    $transactionIds[] = $transaction->id;

                    $this->createTransactionDetails($transaction, [
                        [
                            'txn_type_id' => LoanTxnType::INSTALLMENT->value,
                            'txn_date' => $installment['pay_date'] ?? now(),
                            'amount' => $installment['principle_amount'] ?? 0,
                            'precision_amount' => $installment['principle_amount'] ?? 0,
                            'remark' => 'Principal amount for tenure '.($installment['tenure'] ?? ''),
                        ],
                        [
                            'txn_type_id' => LoanTxnType::INSTALLMENT->value,
                            'txn_date' => $installment['pay_date'] ?? now(),
                            'amount' => $installment['interest_amount_net'] ?? 0,
                            'precision_amount' => $installment['interest_amount_net'] ?? 0,
                            'remark' => 'Interest amount for tenure '.($installment['tenure'] ?? ''),
                        ],
                    ]);
                }
            });

            return $transactionIds;
        } catch (\Throwable $e) {
            Log::error('Installment transaction generation failed', [
                'loan_id' => $loanId,
                'error' => $e->getMessage(),
            ]);

            throw new \RuntimeException('Failed to generate installment transactions.', 0, $e);
        }
    }

    /**
     * Generate a single loan transaction
     *
     * @param  array  $transactionData  Transaction data
     * @param  array  $detailsData  Array of transaction details data
     * @return LoanTxn Created transaction
     *
     * @throws \RuntimeException
     */
    public function generateLoanTransaction(array $transactionData, array $detailsData = []): LoanTxn
    {
        try {
            return DB::transaction(function () use ($transactionData, $detailsData) {
                $transaction = $this->createLoanTransaction($transactionData);

                if (! empty($detailsData)) {
                    $this->createTransactionDetails($transaction, $detailsData);
                }

                return $transaction;
            });
        } catch (\Throwable $e) {
            Log::error('Loan transaction generation failed', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            throw new \RuntimeException('Failed to generate loan transaction.', 0, $e);
        }
    }

    /**
     * Generate late interest transaction
     */
    public function generateLateInterestTransaction(int $loanId, int $installmentId, int $tenure, string $paymentDate, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::LATE_INTEREST->value,
            'txn_date' => $paymentDate,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Late interest charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::LATE_INTEREST->value,
                'txn_date' => $paymentDate,
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Late interest charge',
            ],
        ]);
    }

    /**
     * Generate legal fee transaction
     */
    public function generateLegalFeeTransaction(int $loanId, int $installmentId, int $tenure, string $paymentDate, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::LEGAL_FEE->value,
            'txn_date' => $paymentDate,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Legal fee charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::LEGAL_FEE->value,
                'txn_date' => $paymentDate,
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Legal fee charge',
            ],
        ]);
    }

    /**
     * Generate miscellaneous charge transaction
     */
    public function generateMiscChargeTransaction(int $loanId, int $installmentId, int $tenure, string $paymentDate, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::MISC_CHARGE->value,
            'txn_date' => $paymentDate,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Miscellaneous charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::MISC_CHARGE->value,
                'txn_date' => $paymentDate,
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Miscellaneous charge',
            ],
        ]);
    }

    /**
     * Generate postage transaction
     */
    public function generatePostageTransaction(int $loanId, int $installmentId, int $tenure, string $paymentDate, float $amount, int $letterTypeId, ?string $arSerial = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::POSTAGE->value,
            'txn_date' => $paymentDate,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $this->generatePostageDescription($tenure, $letterTypeId, $arSerial) ?? 'Postage charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::POSTAGE->value,
                'txn_date' => $paymentDate,
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $this->generatePostageDescription($tenure, $letterTypeId, $arSerial) ?? 'Postage charge',
            ],
        ]);
    }

    /**
     * Create a loan transaction record
     *
     * @param  array  $data  Transaction data
     */
    protected function createLoanTransaction(array $data): LoanTxn
    {
        $txnType = null;
        if (isset($data['loan_txn_type_id'])) {
            $txnTypeModel = LoanTxnTypeModel::find($data['loan_txn_type_id']);
            $txnType = $txnTypeModel?->type?->label();
        }

        return LoanTxn::create([
            'uuid' => Str::uuid(),
            'loan_id' => $data['loan_id'],
            'loan_installment_id' => $data['loan_installment_id'] ?? null,
            'loan_txn_type_id' => $data['loan_txn_type_id'],
            'txn_date' => $data['txn_date'] ?? now(),
            'tenure' => $data['tenure'] ?? null,
            'txn_type' => $txnType,
            'amount' => $data['amount'],
            'status' => $data['status'] ?? LoanTxnStatus::UNPAID,
            'sort_date' => $data['sort_date'] ?? now(),
            'remark' => $data['remark'] ?? null,
        ]);
    }

    /**
     * Create transaction detail records
     */
    protected function createTransactionDetails(LoanTxn $transaction, array $detailsData): void
    {
        foreach ($detailsData as $detail) {
            $txnType = null;
            if (isset($detail['txn_type_id'])) {
                $txnTypeModel = LoanTxnTypeModel::find($detail['txn_type_id']);
                $txnType = $txnTypeModel?->type?->label();
            }

            LoanTxnDetail::create([
                'uuid' => Str::uuid(),
                'loan_txn_id' => $transaction->id,
                'loan_txn_type_id' => $detail['txn_type_id'],
                'txn_type' => $txnType,
                'txn_date' => $detail['txn_date'] ?? now(),
                'amount' => $detail['amount'],
                'precision_amount' => $detail['precision_amount'] ?? $detail['amount'],
                'status' => $detail['status'] ?? LoanTxnStatus::UNPAID,
                'remark' => $detail['remark'] ?? null,
            ]);
        }
    }

    /**
     * Generate payment transaction and create payment records
     */
    public function generatePaymentTransaction(int $loanId, float $amount, string $paymentDate, ?int $paymentMethodId = null, ?string $paymentRefCode = null, ?float $rebateAmount = null, ?string $remark = null): void
    {
        try {
            DB::beginTransaction();

            $paymentMethod = null;
            if ($paymentMethodId) {
                $paymentMethodSelection = Selection::find($paymentMethodId);
                $paymentMethod = $paymentMethodSelection?->value;
            }

            $payment = LoanPayment::create([
                'loan_id' => $loanId,
                'txn_date' => now(),
                'txn_type' => 'Official Receipt - Repayment',
                'payment_ref_code' => $paymentRefCode,
                'payment_method' => $paymentMethod ?? 'Cash',
                'payment_date' => $paymentDate,
                'amount' => $amount,
                'rebate_amount' => $rebateAmount ?? 0,
                'remark' => $remark ?? 'Payment received',
                'status' => LoanPaymentStatus::PAID,
            ]);

            $unpaidTransactions = LoanTxn::withLoanId($loanId)->orderedByTypeAndDate()->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])->get();

            $remainingAmount = $amount;

            $installmentFirstPaid = null;

            foreach ($unpaidTransactions as $transaction) {
                if ($remainingAmount <= 0) {
                    break;
                }

                $txnBalance = $transaction->amount - $transaction->loanPaymentDetails->sum('amount');
                if ($txnBalance <= 0) {
                    $transaction->update(['status' => LoanTxnStatus::PAID]);

                    continue;
                }

                if ($installmentFirstPaid === null && $transaction->loan_txn_type_id === LoanTxnType::INSTALLMENT->value) {
                    $installmentFirstPaid = $transaction->loanInstallment->tenure;
                }

                $transactionAmount = $transaction->amount;
                $paymentAmount = min($remainingAmount, $transactionAmount);

                LoanPaymentDetail::create([
                    'loan_payment_id' => $payment->id,
                    'loan_txn_id' => $transaction->id,
                    'amount' => $paymentAmount,
                ]);

                if ($paymentAmount >= $transactionAmount) {
                    $transaction->update(['status' => LoanTxnStatus::PAID]);
                } else {
                    $transaction->update(['status' => LoanTxnStatus::PARTIALLY_PAID]);
                }

                $txnBalance = $transaction->amount - $transaction->loanPaymentDetails->sum('amount');
                if ($txnBalance <= 0) {
                    $transaction->update(['status' => LoanTxnStatus::PAID]);
                }

                $remainingAmount -= $paymentAmount;
            }

            if ($installmentFirstPaid !== null && $paymentRefCode) {
                $payment->update([
                    'payment_ref_code' => "{$paymentRefCode} - {$installmentFirstPaid}",
                ]);
            }

            // Check if all transactions are paid
            // If all transactions are paid, update loan status to completed
            $unpaidTransactions = LoanTxn::withLoanId($loanId)->orderedByTypeAndDate()->byStatus([LoanTxnStatus::UNPAID, LoanTxnStatus::PARTIALLY_PAID])->get();
            if ($unpaidTransactions->count() === 0) {
                $loan = Loan::find($loanId);
                $loan->update(['status' => LoanStatus::COMPLETED]);
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Payment transaction generation failed', [
                'loan_id' => $loanId,
                'error' => $e->getMessage(),
            ]);
            throw new \RuntimeException('Failed to generate payment transaction.', 0, $e);
        }
    }

    /**
     * Generate a description for postage transactions based on tenure and letter type
     *
     * @param  int  $tenure  The tenure number
     * @param  int  $letterTypeId  The letter type ID
     * @param  string|null  $arSerial  The AR serial number (optional)
     */
    public function generatePostageDescription(int $tenure, int $letterTypeId, ?string $arSerial = null): ?string
    {
        if (! $tenure || ! $letterTypeId) {
            return null;
        }

        $letterType = Selection::find($letterTypeId)?->value;

        if (! $letterType) {
            return null;
        }

        $ordinal = $this->ordinal($tenure);

        if (stripos($letterType, 'SMS reminder') !== false) {
            return "{$ordinal} tenure SMS reminder";
        }

        if (stripos($letterType, 'Notice of Demand') !== false && $arSerial) {
            return "{$ordinal} tenure Notice of Demand: {$arSerial}";
        }

        return null;
    }

    /**
     * Convert a number to its ordinal representation (e.g., 1st, 2nd, 3rd)
     *
     * @param  int  $number  The number to convert
     */
    protected function ordinal($number): string
    {
        $ends = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];

        if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
            return $number.'th';
        }

        return $number.($ends[$number % 10] ?? 'th');
    }
}
