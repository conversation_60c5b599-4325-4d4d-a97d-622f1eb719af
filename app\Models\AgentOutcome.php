<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AgentOutcome model for managing agent outcome
 */
class AgentOutcome extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'company_id',
        'agent_id',
        'outcome_types_id',
        'outcome_types',
        'amount',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'agent_id' => 'integer',
            'outcome_types_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the companies associated with this agent outcome type.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the agent outcome type information.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(AgentProfile::class, 'agent_id');
    }

    /**
     * Get the agent outcome type information.
     */
    public function outcomeType(): BelongsTo
    {
        return $this->belongsTo(AgentOutcomeType::class, 'outcome_types_id');
    }

    /**
     * Get the user's resolve hierarchy.
     */
    public function getResolveHierarchy(): array
    {
        $agentOutcomeProfile = $this;

        if (! $this) {
            return ['headquarter' => null, 'company' => null];
        }

        if ($directCompany = $agentOutcomeProfile->companies->first()) {
            $headquarter = ($directCompany->headquarter_id && $directCompany->headquarter)
                ? $directCompany->headquarter
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $directCompany,
            ];
        }

        return [
            'headquarter' => null,
            'company' => null,
        ];
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return self::query();
        }

        return self::whereIn('company_id', $user->getAccessibleCompanyIds());
    }
}
