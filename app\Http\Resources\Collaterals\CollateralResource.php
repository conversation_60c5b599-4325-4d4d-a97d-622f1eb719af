<?php

namespace App\Http\Resources\Collaterals;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollateralResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $valuer = $this->valuers->firstWhere('is_primary', true);

        $customerType = $this->selection_customer_type_id;

        switch ($customerType) {
            case 28: // Personal
                $name = $this->name;
                $identityNo = $this->identity_no;
                break;

            case 29: // Company
                $name = $this->company_name;
                $identityNo = $this->business_registration_no;
                break;

            default:
                $name = null;
                $identityNo = null;
                break;
        }

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'company' => $this->whenLoaded('company', fn () => [
                'id' => $this->company->id,
                'display_name' => $this->company->display_name,
                'headquarter' => $this->company->headquarter ? fn () => [
                    'id' => $this->company->headquarter->id,
                    'display_name' => $this->company->headquarter->display_name,
                ] : null,
            ]),
            'team' => $this->whenLoaded('team', fn () => [
                'id' => $this->team->id,
                'name' => $this->team->name,
            ]),
            'selection_type_id' => $this->selection_type_id,
            'selection_customer_type_id' => $this->selection_customer_type_id,
            'customer_type_selection' => $this->customerTypeSelection ? $this->customerTypeSelection->value : null,
            'type_selection' => $this->typeSelection ? $this->typeSelection->value : null,
            'name' => $name,
            'identity_no' => $identityNo,
            'status' => $this->status->value,
            'status_label' => $this->status->label(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
            'property' => $this->property ? [
                'address' => $this->property->address ? [
                    'line_1' => $this->property->address->line_1,
                ] : null,
            ] : null,
            'valuer' => $this->whenLoaded('valuers', fn () => $this->valuers->map(fn ($valuer) => [
                'valuer' => $valuer->valuer,
                'valuation_received_date' => $valuer->valuation_received_date,
                'land_search_received_date' => $valuer->land_search_received_date,
            ])),
            'canUpdate' => $request->user()->can('update', $this->resource),
            'canView' => $request->user()->can('view', $this->resource),
            'canDelete' => $request->user()->can('delete', $this->resource),
        ];
    }
}
