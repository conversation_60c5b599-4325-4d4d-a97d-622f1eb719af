<?php

namespace App\Http\Requests\Loans;

use App\Http\Requests\BaseRequest;
use App\Models\LoanTxn;

class StoreLoanTransactionRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $baseRules = [
            'selection_transaction_type_id' => ['required', 'integer', 'exists:selections,id'],
            'amount' => ['required', 'numeric', 'min:0.01'],
            'payment_date' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:500'],
        ];

        $transactionTypeId = (int) $this->input('selection_transaction_type_id');
        $letterTypeId = (int) $this->input('selection_letter_type_id');

        if ($transactionTypeId === 390) { // Official Receipt - Repayment
            $loanId = $this->route('loan')->id;
            $balance = LoanTxn::getLoanBalance($loanId);

            return array_merge($baseRules, [
                'amount' => ['required', 'numeric', 'min:0.01', 'max:'.$balance],
                'selection_payment_method_id' => ['required', 'integer', 'exists:selections,id'],
                'payment_ref_no' => ['required', 'string', 'max:255'],
                'rebate_amount' => ['nullable', 'numeric', 'min:0'],
            ]);
        }

        if ($transactionTypeId === 391) { // Postage
            return array_merge($baseRules, [
                'no_instalment' => ['required', 'integer', 'min:1'],
                'selection_letter_type_id' => ['required', 'integer', 'exists:selections,id'],
                'ar_serial_no' => $letterTypeId === 396 ? ['required', 'string', 'max:255'] : ['nullable', 'string', 'max:255'],
            ]);
        }

        return $baseRules;
    }

    public function messages(): array
    {
        $balance = LoanTxn::getLoanBalance($this->route('loan')->id);
        $formattedBalance = number_format((float) $balance, 2);

        return [
            'amount.max' => "The amount field must not be greater than {$formattedBalance}.",
        ];
    }
}
